@import "tailwindcss";

:root {
  --background: 251 252 254; /* Modern light background */
  --foreground: 15 23 42; /* slate-900 */
  --card: 255 255 255; /* white */
  --card-foreground: 15 23 42; /* slate-900 */
  --popover: 255 255 255; /* white */
  --popover-foreground: 15 23 42; /* slate-900 */
  --primary: 99 102 241; /* Modern indigo-500 */
  --primary-foreground: 255 255 255; /* white */
  --secondary: 248 250 252; /* slate-50 */
  --secondary-foreground: 71 85 105; /* slate-600 */
  --muted: 241 245 249; /* slate-100 */
  --muted-foreground: 100 116 139; /* slate-500 */
  --accent: 239 246 255; /* blue-50 */
  --accent-foreground: 30 64 175; /* blue-800 */
  --destructive: 239 68 68; /* red-500 */
  --destructive-foreground: 255 255 255; /* white */
  --border: 226 232 240; /* slate-200 */
  --input: 226 232 240; /* slate-200 */
  --ring: 99 102 241; /* indigo-500 */
  --radius: 0.75rem;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark {
  --background: 2 6 23; /* Modern dark background */
  --foreground: 248 250 252; /* slate-50 */
  --card: 15 23 42; /* slate-900 */
  --card-foreground: 248 250 252; /* slate-50 */
  --popover: 15 23 42; /* slate-900 */
  --popover-foreground: 248 250 252; /* slate-50 */
  --primary: 129 140 248; /* indigo-400 */
  --primary-foreground: 15 23 42; /* slate-900 */
  --secondary: 30 41 59; /* slate-800 */
  --secondary-foreground: 203 213 225; /* slate-300 */
  --muted: 30 41 59; /* slate-800 */
  --muted-foreground: 148 163 184; /* slate-400 */
  --accent: 30 41 59; /* slate-800 */
  --accent-foreground: 203 213 225; /* slate-300 */
  --destructive: 248 113 113; /* red-400 */
  --destructive-foreground: 15 23 42; /* slate-900 */
  --border: 51 65 85; /* slate-700 */
  --input: 51 65 85; /* slate-700 */
  --ring: 129 140 248; /* indigo-400 */
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

* {
  border-color: rgb(var(--border));
}

body {
  background-color: rgb(var(--background));
  color: rgb(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
  transition: background-color 0.3s ease, color 0.3s ease;
  overflow-x: hidden;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern glass effect for cards */
.glass-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.dark .glass-card {
  background: rgba(15, 23, 42, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
}

/* Modern gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, rgb(99, 102, 241) 0%, rgb(139, 92, 246) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, rgb(59, 130, 246) 0%, rgb(16, 185, 129) 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, rgb(236, 72, 153) 0%, rgb(239, 68, 68) 100%);
}

/* Modern button styles */
.btn-modern {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease-out;
  transform-origin: center;
}

.btn-modern::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.7s ease;
}

.btn-modern:hover::before {
  transform: translateX(100%);
}

.btn-modern:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: scale(1.05);
}

/* Modern input styles */
.input-modern {
  transition: all 0.3s ease-out;
  border: 1px solid rgb(var(--border));
  border-radius: var(--radius);
}

.input-modern:focus {
  outline: none;
  border-color: rgb(var(--primary));
  box-shadow: 0 0 0 2px rgba(var(--primary), 0.2);
}

.input-modern:hover {
  border-color: rgba(var(--primary), 0.5);
}

/* Theme transition for all elements */
*,
*::before,
*::after {
  transition: 
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease,
    box-shadow 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgb(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: rgb(var(--muted-foreground));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--primary));
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: rgb(var(--muted-foreground)) rgb(var(--muted));
}

/* Focus ring improvements */
.focus-visible\:ring-2:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgb(var(--ring));
}

/* Mobile-specific animations and transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slide-in {
  animation: slideIn 0.6s ease-out forwards;
}

/* Modern animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Modern card styles */
.modern-card {
  background: rgb(var(--card));
  border: 1px solid rgb(var(--border));
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.modern-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Modern gradient text */
.gradient-text {
  background: linear-gradient(135deg, rgb(99, 102, 241), rgb(139, 92, 246));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Modern badge styles */
.badge-modern {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  transition: all 0.2s ease;
}

.badge-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgb(16, 185, 129);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.badge-pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: rgb(245, 158, 11);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.badge-error {
  background-color: rgba(239, 68, 68, 0.1);
  color: rgb(239, 68, 68);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Modern layout improvements */
.container-modern {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-modern {
    padding: 0 2rem;
  }
}

/* Modern typography */
.text-gradient {
  background: linear-gradient(135deg, rgb(99, 102, 241), rgb(139, 92, 246));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-balance {
  text-wrap: balance;
}

/* Focus improvements */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus-visible {
  outline: 2px solid rgb(var(--ring));
  outline-offset: 2px;
}

/* Modern loading spinner */
.spinner-modern {
  width: 24px;
  height: 24px;
  border: 2px solid rgb(var(--muted));
  border-top: 2px solid rgb(var(--primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Improved mobile scrolling */
html {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Mobile-friendly form elements */
input, textarea, select {
  font-size: 16px; /* Prevents zoom on iOS */
}

@media (max-width: 640px) {
  input, textarea, select {
    font-size: 16px !important;
  }
}

/* Custom animations */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes bounce-soft {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.4s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.4s ease-out;
}

.animate-pulse-slow {
  animation: pulse-slow 3s ease-in-out infinite;
}

.animate-bounce-soft {
  animation: bounce-soft 2s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
}

/* Skeleton loading animation */
.skeleton {
  background: linear-gradient(
    90deg,
    rgb(var(--muted)) 0%,
    rgb(var(--muted-foreground) / 0.1) 50%,
    rgb(var(--muted)) 100%
  );
  background-size: 200px 100%;
  background-repeat: no-repeat;
  border-radius: 4px;
  display: inline-block;
  line-height: 1;
  width: 100%;
}

/* Improved button hover effects */
.button-hover-lift {
  transition: all 0.2s ease;
}

.button-hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgb(var(--primary) / 0.15);
}

/* Backdrop blur support */
.backdrop-blur-glass {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background-color: rgb(var(--background) / 0.8);
}

/* Dark mode image filters */
.dark img {
  filter: brightness(0.9) contrast(1.1);
}

/* Selection styling */
::selection {
  background-color: rgb(var(--primary) / 0.2);
  color: rgb(var(--primary-foreground));
}

/* Improved form field styling */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
textarea,
select {
  transition: all 0.2s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="url"]:focus,
textarea:focus,
select:focus {
  box-shadow: 0 0 0 2px rgb(var(--ring) / 0.2);
  border-color: rgb(var(--ring));
}

/* Loading states */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgb(var(--muted-foreground) / 0.1),
    transparent
  );
  transform: translateX(-100%);
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Mobile optimizations for booking page */
@media (max-width: 768px) {
  .booking-header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .booking-header-photo {
    margin-bottom: 1rem;
  }
  
  .booking-header-info {
    align-items: center;
  }
}

/* Additional responsive utilities */
.responsive-grid-1 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .responsive-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .responsive-grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Safe area support for devices with notches */
@supports (padding: max(0px)) {
  .safe-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .safe-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

/* Prevent content jumping during loading */
.min-h-touch {
  min-height: 44px;
}

/* Mobile-first breakpoint utilities */
@media (max-width: 640px) {
  .mobile-full-width {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
  }
  
  .mobile-px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
